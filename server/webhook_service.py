"""
Webhook notification service
"""
import httpx
from models import WebhookPayload
from job_storage import get_job


async def send_webhook_notification(job_id: str, webhook_url: str):
    """Send webhook notification when job completes"""
    try:
        job_data = get_job(job_id)
        if not job_data:
            print(f"Job {job_id} not found for webhook notification")
            return

        payload = WebhookPayload(
            job_id=job_id,
            status=job_data["status"],
            result=job_data.get("result"),
            error=job_data.get("error"),
            completed_at=job_data["completed_at"]
        )

        async with httpx.AsyncClient() as client:
            response = await client.post(
                webhook_url,
                json=payload.model_dump(),
                timeout=30.0
            )
            response.raise_for_status()

    except Exception as e:
        # Log webhook error but don't fail the job
        print(f"Webhook notification failed for job {job_id}: {str(e)}")
