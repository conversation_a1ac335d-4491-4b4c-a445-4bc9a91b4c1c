"""
Data models for the AI service API
"""
from datetime import datetime
from enum import Enum
from typing import Dict, Optional, Any
from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class OpenAIRequest(BaseModel):
    prompt: str = Field(..., description="The prompt to send to OpenAI")
    model: str = Field(default="gpt-3.5-turbo",
                       description="OpenAI model to use")
    max_tokens: Optional[int] = Field(
        default=1000, description="Maximum tokens in response")
    temperature: Optional[float] = Field(
        default=0.7, description="Temperature for response generation")
    webhook_url: str = Field(...,
                             description="URL to send the result to when job completes")
    api_key: str = Field(..., description="OpenAI API key")


class GeminiRequest(BaseModel):
    prompt: str = Field(..., description="The prompt to send to Google Gemini")
    model: str = Field(default="gemini-pro", description="Gemini model to use")
    max_tokens: Optional[int] = Field(
        default=1000, description="Maximum tokens in response")
    temperature: Optional[float] = Field(
        default=0.7, description="Temperature for response generation")
    webhook_url: str = Field(...,
                             description="URL to send the result to when job completes")
    api_key: str = Field(..., description="Google AI API key")


class JobResponse(BaseModel):
    job_id: str
    status: JobStatus
    message: str


class JobStatusResponse(BaseModel):
    job_id: str
    status: JobStatus
    created_at: datetime
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class WebhookPayload(BaseModel):
    job_id: str
    status: JobStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    completed_at: datetime
