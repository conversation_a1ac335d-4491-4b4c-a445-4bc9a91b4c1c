import asyncio
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Optional, Any
import httpx
import openai
from fastapi import Fast<PERSON><PERSON>, BackgroundTasks, HTTPException
from pydantic import BaseModel, Field


app = FastAPI(title="Truyen Moi Giay API", version="1.0.0")

# In-memory job storage (in production, use Redis or database)
jobs: Dict[str, Dict[str, Any]] = {}


class JobStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class OpenAIRequest(BaseModel):
    prompt: str = Field(..., description="The prompt to send to OpenAI")
    model: str = Field(default="gpt-3.5-turbo", description="OpenAI model to use")
    max_tokens: Optional[int] = Field(default=1000, description="Maximum tokens in response")
    temperature: Optional[float] = Field(default=0.7, description="Temperature for response generation")
    webhook_url: str = Field(..., description="URL to send the result to when job completes")
    api_key: str = Field(..., description="OpenAI API key")


class JobResponse(BaseModel):
    job_id: str
    status: JobStatus
    message: str


class JobStatusResponse(BaseModel):
    job_id: str
    status: JobStatus
    created_at: datetime
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class WebhookPayload(BaseModel):
    job_id: str
    status: JobStatus
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    completed_at: datetime


async def process_openai_request(job_id: str, request_data: OpenAIRequest):
    """Background task to process OpenAI API request"""
    try:
        # Update job status to processing
        jobs[job_id]["status"] = JobStatus.PROCESSING

        # Initialize OpenAI client
        client = openai.OpenAI(api_key=request_data.api_key)

        # Make OpenAI API call
        response = client.chat.completions.create(
            model=request_data.model,
            messages=[{"role": "user", "content": request_data.prompt}],
            max_tokens=request_data.max_tokens,
            temperature=request_data.temperature
        )

        # Extract result
        result = {
            "content": response.choices[0].message.content,
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }

        # Update job with result
        jobs[job_id].update({
            "status": JobStatus.COMPLETED,
            "result": result,
            "completed_at": datetime.now()
        })

        # Send webhook notification
        await send_webhook_notification(job_id, request_data.webhook_url)

    except Exception as e:
        # Update job with error
        jobs[job_id].update({
            "status": JobStatus.FAILED,
            "error": str(e),
            "completed_at": datetime.now()
        })

        # Send webhook notification with error
        await send_webhook_notification(job_id, request_data.webhook_url)


async def send_webhook_notification(job_id: str, webhook_url: str):
    """Send webhook notification when job completes"""
    try:
        job_data = jobs[job_id]
        payload = WebhookPayload(
            job_id=job_id,
            status=job_data["status"],
            result=job_data.get("result"),
            error=job_data.get("error"),
            completed_at=job_data["completed_at"]
        )

        async with httpx.AsyncClient() as client:
            response = await client.post(
                webhook_url,
                json=payload.model_dump(),
                timeout=30.0
            )
            response.raise_for_status()

    except Exception as e:
        # Log webhook error but don't fail the job
        print(f"Webhook notification failed for job {job_id}: {str(e)}")


@app.post("/openai/submit", response_model=JobResponse)
async def submit_openai_request(request: OpenAIRequest, background_tasks: BackgroundTasks):
    """Submit an OpenAI API request for background processing"""

    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Create job record
    jobs[job_id] = {
        "status": JobStatus.PENDING,
        "created_at": datetime.now(),
        "request": request.model_dump(exclude={"api_key"}),  # Don't store API key
        "completed_at": None,
        "result": None,
        "error": None
    }

    # Add background task
    background_tasks.add_task(process_openai_request, job_id, request)

    return JobResponse(
        job_id=job_id,
        status=JobStatus.PENDING,
        message="Job submitted successfully. You will receive a webhook notification when complete."
    )


@app.get("/openai/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """Get the status of a submitted job"""

    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")

    job_data = jobs[job_id]

    return JobStatusResponse(
        job_id=job_id,
        status=job_data["status"],
        created_at=job_data["created_at"],
        completed_at=job_data.get("completed_at"),
        result=job_data.get("result"),
        error=job_data.get("error")
    )


@app.get("/openai/jobs")
async def list_jobs():
    """List all jobs (for debugging/monitoring)"""
    return {
        "total_jobs": len(jobs),
        "jobs": [
            {
                "job_id": job_id,
                "status": job_data["status"],
                "created_at": job_data["created_at"],
                "completed_at": job_data.get("completed_at")
            }
            for job_id, job_data in jobs.items()
        ]
    }


@app.get("/")
def read_root():
    return {"message": "Welcome to Truyen Moi Giay API!"}
