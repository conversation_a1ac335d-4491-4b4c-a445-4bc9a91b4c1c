# AI Background Job API

This FastAPI application provides endpoints for submitting OpenAI and Google Gemini AI API requests as background jobs with webhook notifications.

## 🏗️ **Modular Architecture**

The codebase is now organized into clean, maintainable modules:

- **`main.py`** - FastAPI application entry point
- **`models.py`** - Pydantic data models and enums
- **`routes.py`** - API route definitions
- **`ai_services.py`** - OpenAI and Gemini AI service implementations
- **`webhook_service.py`** - Webhook notification handling
- **`job_storage.py`** - Job data storage management

## Features

- ✅ Submit OpenAI API requests for background processing
- ✅ Submit Google Gemini AI API requests for background processing
- ✅ Job status tracking with unique IDs
- ✅ Webhook notifications when jobs complete
- ✅ Error handling and proper status updates
- ✅ RESTful endpoints for job management

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Start the server:
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## API Endpoints

### 1. Submit OpenAI Request
**POST** `/openai/submit`

Submit an OpenAI API request for background processing.

**Request Body:**
```json
{
  "prompt": "Write a short story about a robot",
  "model": "gpt-3.5-turbo",
  "max_tokens": 1000,
  "temperature": 0.7,
  "webhook_url": "https://your-webhook-endpoint.com/callback",
  "api_key": "your-openai-api-key"
}
```

**Response:**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "pending",
  "message": "Job submitted successfully. You will receive a webhook notification when complete."
}
```

### 2. Submit Google Gemini Request
**POST** `/gemini/submit`

Submit a Google Gemini AI API request for background processing.

**Request Body:**
```json
{
  "prompt": "Write a haiku about artificial intelligence",
  "model": "gemini-pro",
  "max_tokens": 1000,
  "temperature": 0.7,
  "webhook_url": "https://your-webhook-endpoint.com/callback",
  "api_key": "your-google-ai-api-key"
}
```

**Response:**
```json
{
  "job_id": "456e7890-e89b-12d3-a456-426614174001",
  "status": "pending",
  "message": "Gemini job submitted successfully. You will receive a webhook notification when complete."
}
```

### 3. Check OpenAI Job Status
**GET** `/openai/status/{job_id}`

Get the current status of a submitted OpenAI job.

### 4. Check Gemini Job Status
**GET** `/gemini/status/{job_id}`

Get the current status of a submitted Gemini job.

**Response:**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "created_at": "2024-01-01T12:00:00",
  "completed_at": "2024-01-01T12:00:30",
  "result": {
    "content": "Generated text from OpenAI...",
    "model": "gpt-3.5-turbo",
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 100,
      "total_tokens": 110
    }
  },
  "error": null
}
```

### 5. List All Jobs
**GET** `/jobs`

List all jobs (OpenAI and Gemini) for monitoring purposes.

## Webhook Payload

When a job completes (successfully or with error), a POST request will be sent to your webhook URL:

```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "result": {
    "content": "Generated text...",
    "model": "gpt-3.5-turbo",
    "usage": {...}
  },
  "error": null,
  "completed_at": "2024-01-01T12:00:30"
}
```

## Job Statuses

- `pending`: Job has been submitted and is waiting to be processed
- `processing`: Job is currently being processed by OpenAI API
- `completed`: Job completed successfully
- `failed`: Job failed due to an error

## Example Usage

```bash
# Submit OpenAI job
curl -X POST "http://localhost:8000/openai/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a short story about AI",
    "model": "gpt-3.5-turbo",
    "max_tokens": 500,
    "webhook_url": "https://your-webhook.com/callback",
    "api_key": "your-openai-api-key"
  }'

# Submit Gemini job
curl -X POST "http://localhost:8000/gemini/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a haiku about technology",
    "model": "gemini-pro",
    "max_tokens": 200,
    "webhook_url": "https://your-webhook.com/callback",
    "api_key": "your-google-ai-api-key"
  }'

# Check job status
curl "http://localhost:8000/openai/status/{job_id}"
curl "http://localhost:8000/gemini/status/{job_id}"
```

## Security Notes

- API keys are not stored in job records for security
- Consider implementing authentication for production use
- Use HTTPS for webhook URLs in production
- Consider rate limiting for production deployments

## Production Considerations

- Replace in-memory job storage with Redis or a database
- Add proper logging and monitoring
- Implement job cleanup/expiration
- Add authentication and authorization
- Use environment variables for configuration
