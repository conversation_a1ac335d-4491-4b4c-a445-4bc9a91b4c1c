"""
Job storage management
"""
from typing import Dict, Any

# In-memory job storage (in production, use Redis or database)
jobs: Dict[str, Dict[str, Any]] = {}


def get_job(job_id: str) -> Dict[str, Any]:
    """Get job data by ID"""
    return jobs.get(job_id, {})


def create_job(job_id: str, job_data: Dict[str, Any]) -> None:
    """Create a new job"""
    jobs[job_id] = job_data


def update_job(job_id: str, updates: Dict[str, Any]) -> None:
    """Update job data"""
    if job_id in jobs:
        jobs[job_id].update(updates)


def job_exists(job_id: str) -> bool:
    """Check if job exists"""
    return job_id in jobs


def get_all_jobs() -> Dict[str, Dict[str, Any]]:
    """Get all jobs"""
    return jobs
