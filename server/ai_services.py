"""
AI service implementations for OpenAI and Google Gemini
"""
from datetime import datetime
import openai
from google import genai
from google.genai import types

from models import OpenAIRequest, GeminiRequest, JobStatus
from job_storage import update_job
from webhook_service import send_webhook_notification


async def process_openai_request(job_id: str, request_data: OpenAIRequest):
    """Background task to process OpenAI API request"""
    try:
        # Update job status to processing
        update_job(job_id, {"status": JobStatus.PROCESSING})

        # Initialize OpenAI client
        client = openai.OpenAI(api_key=request_data.api_key)

        # Make OpenAI API call
        response = client.chat.completions.create(
            model=request_data.model,
            messages=[{"role": "user", "content": request_data.prompt}],
            max_tokens=request_data.max_tokens,
            temperature=request_data.temperature
        )

        # Extract result
        result = {
            "content": response.choices[0].message.content,
            "model": response.model,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                "total_tokens": response.usage.total_tokens if response.usage else 0
            }
        }

        # Update job with result
        update_job(job_id, {
            "status": JobStatus.COMPLETED,
            "result": result,
            "completed_at": datetime.now()
        })

        # Send webhook notification
        await send_webhook_notification(job_id, request_data.webhook_url)

    except Exception as e:
        # Update job with error
        update_job(job_id, {
            "status": JobStatus.FAILED,
            "error": str(e),
            "completed_at": datetime.now()
        })

        # Send webhook notification with error
        await send_webhook_notification(job_id, request_data.webhook_url)


async def process_gemini_request(job_id: str, request_data: GeminiRequest):
    """Background task to process Google Gemini API request"""
    try:
        # Update job status to processing
        update_job(job_id, {"status": JobStatus.PROCESSING})

        # Initialize Gemini client
        client = genai.Client(api_key=request_data.api_key)

        # Generate content
        response = client.models.generate_content(
            model=request_data.model,
            contents=request_data.prompt,
            config=types.GenerateContentConfig(
                candidate_count=1,
                max_output_tokens=request_data.max_tokens,
                temperature=request_data.temperature,
            )
        )

        # Extract result
        content = ""
        if response.candidates and len(response.candidates) > 0:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts and len(candidate.content.parts) > 0:
                content = candidate.content.parts[0].text or ""

        result = {
            "content": content,
            "model": request_data.model,
            "usage": {
                "prompt_tokens": response.usage_metadata.prompt_token_count if response.usage_metadata else 0,
                "completion_tokens": response.usage_metadata.candidates_token_count if response.usage_metadata else 0,
                "total_tokens": response.usage_metadata.total_token_count if response.usage_metadata else 0
            }
        }

        # Update job with result
        update_job(job_id, {
            "status": JobStatus.COMPLETED,
            "result": result,
            "completed_at": datetime.now()
        })

        # Send webhook notification
        await send_webhook_notification(job_id, request_data.webhook_url)

    except Exception as e:
        # Update job with error
        update_job(job_id, {
            "status": JobStatus.FAILED,
            "error": str(e),
            "completed_at": datetime.now()
        })

        # Send webhook notification with error
        await send_webhook_notification(job_id, request_data.webhook_url)
