"""
API routes for AI services
"""
import uuid
from datetime import datetime
from fastapi import APIRouter, BackgroundTasks, HTTPException

from models import (
    OpenAIRequest, GeminiRequest, JobResponse, JobStatusResponse, JobStatus
)
from job_storage import create_job, get_job, job_exists, get_all_jobs
from ai_services import process_openai_request, process_gemini_request

router = APIRouter()


@router.post("/openai/submit", response_model=JobResponse)
async def submit_openai_request(request: OpenAIRequest, background_tasks: BackgroundTasks):
    """Submit an OpenAI API request for background processing"""

    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Create job record
    job_data = {
        "status": JobStatus.PENDING,
        "created_at": datetime.now(),
        "request": request.model_dump(exclude={"api_key"}),  # Don't store API key
        "completed_at": None,
        "result": None,
        "error": None
    }
    create_job(job_id, job_data)

    # Add background task
    background_tasks.add_task(process_openai_request, job_id, request)

    return JobResponse(
        job_id=job_id,
        status=JobStatus.PENDING,
        message="Job submitted successfully. You will receive a webhook notification when complete."
    )


@router.post("/gemini/submit", response_model=JobResponse)
async def submit_gemini_request(request: GeminiRequest, background_tasks: BackgroundTasks):
    """Submit a Google Gemini API request for background processing"""

    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Create job record
    job_data = {
        "status": JobStatus.PENDING,
        "created_at": datetime.now(),
        "request": request.model_dump(exclude={"api_key"}),  # Don't store API key
        "completed_at": None,
        "result": None,
        "error": None
    }
    create_job(job_id, job_data)

    # Add background task
    background_tasks.add_task(process_gemini_request, job_id, request)

    return JobResponse(
        job_id=job_id,
        status=JobStatus.PENDING,
        message="Gemini job submitted successfully. You will receive a webhook notification when complete."
    )


@router.get("/openai/status/{job_id}", response_model=JobStatusResponse)
async def get_openai_job_status(job_id: str):
    """Get the status of a submitted OpenAI job"""

    if not job_exists(job_id):
        raise HTTPException(status_code=404, detail="Job not found")

    job_data = get_job(job_id)

    return JobStatusResponse(
        job_id=job_id,
        status=job_data["status"],
        created_at=job_data["created_at"],
        completed_at=job_data.get("completed_at"),
        result=job_data.get("result"),
        error=job_data.get("error")
    )


@router.get("/gemini/status/{job_id}", response_model=JobStatusResponse)
async def get_gemini_job_status(job_id: str):
    """Get the status of a submitted Gemini job"""

    if not job_exists(job_id):
        raise HTTPException(status_code=404, detail="Job not found")

    job_data = get_job(job_id)

    return JobStatusResponse(
        job_id=job_id,
        status=job_data["status"],
        created_at=job_data["created_at"],
        completed_at=job_data.get("completed_at"),
        result=job_data.get("result"),
        error=job_data.get("error")
    )


@router.get("/jobs")
async def list_all_jobs():
    """List all jobs (OpenAI and Gemini) for monitoring"""
    all_jobs = get_all_jobs()
    return {
        "total_jobs": len(all_jobs),
        "jobs": [
            {
                "job_id": job_id,
                "status": job_data["status"],
                "created_at": job_data["created_at"],
                "completed_at": job_data.get("completed_at")
            }
            for job_id, job_data in all_jobs.items()
        ]
    }
