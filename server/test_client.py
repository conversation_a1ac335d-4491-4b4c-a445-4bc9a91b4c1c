#!/usr/bin/env python3
"""
Test client for the OpenAI Background Job API
"""

import asyncio
import json
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import httpx


class WebhookHandler(BaseHTTPRequestHandler):
    """Simple webhook server to receive job completion notifications"""
    
    def do_POST(self):
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            payload = json.loads(post_data.decode('utf-8'))
            print(f"\n🎉 Webhook received for job {payload['job_id']}:")
            print(f"   Status: {payload['status']}")
            if payload.get('result'):
                print(f"   Content: {payload['result']['content'][:100]}...")
                print(f"   Tokens used: {payload['result']['usage']['total_tokens']}")
            if payload.get('error'):
                print(f"   Error: {payload['error']}")
            print(f"   Completed at: {payload['completed_at']}")
            
        except Exception as e:
            print(f"Error processing webhook: {e}")
        
        # Send response
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(b'{"status": "received"}')
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass


def start_webhook_server(port=8001):
    """Start a simple webhook server for testing"""
    server = HTTPServer(('localhost', port), WebhookHandler)
    print(f"🔗 Webhook server started on http://localhost:{port}")
    server.serve_forever()


async def test_openai_api():
    """Test the OpenAI background job API"""
    
    # Start webhook server in background
    webhook_thread = threading.Thread(
        target=start_webhook_server, 
        args=(8001,), 
        daemon=True
    )
    webhook_thread.start()
    
    # Wait a moment for webhook server to start
    await asyncio.sleep(1)
    
    # API configuration
    api_base_url = "http://localhost:8000"
    webhook_url = "http://localhost:8001"
    
    # Replace with your actual OpenAI API key
    openai_api_key = "your-openai-api-key-here"
    
    if openai_api_key == "your-openai-api-key-here":
        print("❌ Please set your OpenAI API key in the test_client.py file")
        return
    
    async with httpx.AsyncClient() as client:
        print("🚀 Testing OpenAI Background Job API\n")
        
        # 1. Submit a job
        print("1️⃣ Submitting OpenAI job...")
        job_request = {
            "prompt": "Write a very short haiku about programming",
            "model": "gpt-3.5-turbo",
            "max_tokens": 100,
            "temperature": 0.7,
            "webhook_url": webhook_url,
            "api_key": openai_api_key
        }
        
        try:
            response = await client.post(
                f"{api_base_url}/openai/submit",
                json=job_request,
                timeout=30.0
            )
            response.raise_for_status()
            job_data = response.json()
            
            job_id = job_data["job_id"]
            print(f"   ✅ Job submitted successfully!")
            print(f"   Job ID: {job_id}")
            print(f"   Status: {job_data['status']}")
            
        except Exception as e:
            print(f"   ❌ Failed to submit job: {e}")
            return
        
        # 2. Check job status periodically
        print(f"\n2️⃣ Monitoring job status...")
        max_attempts = 30
        attempt = 0
        
        while attempt < max_attempts:
            try:
                response = await client.get(f"{api_base_url}/openai/status/{job_id}")
                response.raise_for_status()
                status_data = response.json()
                
                print(f"   Attempt {attempt + 1}: Status = {status_data['status']}")
                
                if status_data['status'] in ['completed', 'failed']:
                    print(f"   ✅ Job finished with status: {status_data['status']}")
                    
                    if status_data['status'] == 'completed':
                        print(f"   📝 Result: {status_data['result']['content']}")
                        print(f"   📊 Tokens: {status_data['result']['usage']['total_tokens']}")
                    else:
                        print(f"   ❌ Error: {status_data['error']}")
                    break
                
                await asyncio.sleep(2)
                attempt += 1
                
            except Exception as e:
                print(f"   ❌ Failed to check status: {e}")
                break
        
        if attempt >= max_attempts:
            print(f"   ⏰ Timeout waiting for job completion")
        
        # 3. List all jobs
        print(f"\n3️⃣ Listing all jobs...")
        try:
            response = await client.get(f"{api_base_url}/openai/jobs")
            response.raise_for_status()
            jobs_data = response.json()
            
            print(f"   📋 Total jobs: {jobs_data['total_jobs']}")
            for job in jobs_data['jobs']:
                print(f"   - {job['job_id']}: {job['status']} (created: {job['created_at']})")
                
        except Exception as e:
            print(f"   ❌ Failed to list jobs: {e}")
    
    print(f"\n🎯 Test completed! Keep the webhook server running to see notifications.")
    print(f"   Press Ctrl+C to exit")
    
    # Keep running to receive webhooks
    try:
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print(f"\n👋 Goodbye!")


if __name__ == "__main__":
    print("OpenAI Background Job API Test Client")
    print("=====================================")
    asyncio.run(test_openai_api())
