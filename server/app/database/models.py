"""
SQLAlchemy database models
"""
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, Integer, JSON, Enum as SQLEnum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.models.schemas import JobStatus, AIProvider

Base = declarative_base()


class Job(Base):
    __tablename__ = "jobs"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Job metadata
    status = Column(SQLEnum(JobStatus), nullable=False, default=JobStatus.PENDING)
    provider = Column(SQLEnum(AIProvider), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Input data (stored as JSON)
    input_data = Column(JSON, nullable=False)
    webhook_url = Column(String(500), nullable=False)
    
    # Results
    result_content = Column(Text, nullable=True)
    result_model = Column(String(100), nullable=True)
    
    # Usage tracking
    prompt_tokens = Column(Integer, nullable=True, default=0)
    completion_tokens = Column(Integer, nullable=True, default=0)
    total_tokens = Column(Integer, nullable=True, default=0)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Additional metadata
    processing_time_seconds = Column(Integer, nullable=True)
    
    def __repr__(self):
        return f"<Job(id={self.id}, status={self.status}, provider={self.provider})>"
